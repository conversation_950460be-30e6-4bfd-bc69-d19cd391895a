{"sonarlint.connectedMode.project": {"connectionId": "https-sonarqube-sgrts-com-", "projectKey": "aiodintech_uifort-bo_7201b7cf-28d9-49d6-9e2f-b38835d853b3"}, "eslint.useFlatConfig": true, "eslint.options": {"overrideConfigFile": "config/eslint.config.mjs"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.configPath": "config/prettier.config.mjs", "prettier.requireConfig": true, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "vitest.enable": true, "vitest.configFile": "config/vitest.config.mts", "vitest.rootConfig": "config/vitest.config.mts", "vitest.workspaceConfig": "vitest.workspace.ts"}