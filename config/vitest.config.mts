import path from 'node:path';
import { fileURLToPath } from 'node:url';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';
import { defineConfig } from 'vitest/config';

const dirname =
  typeof __dirname !== 'undefined' ? __dirname : path.dirname(fileURLToPath(import.meta.url));

// Get the project root directory (one level up from config directory)
const projectRoot = path.resolve(dirname, '..');

console.log('Vitest config loading, project root:', projectRoot);

export default defineConfig({
  plugins: [tsconfigPaths(), react()],
  test: {
    root: projectRoot,
    include: ['src/**/*.test.{ts,tsx}', 'src/**/*.spec.{ts,tsx}'],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.next/**',
      '**/coverage/**',
      'src/**/constants/**',
      'src/**/types/**',
      'src/**/router/**',
      'src/**/styles/**',
      'src/**/mocks/**',
      'src/**/*.style.{ts,tsx}',
      'src/**/*.styles.{ts,tsx}',
      'src/**/*.mock.{ts,tsx}',
    ],
    environment: 'jsdom',
    globals: true,
    typecheck: {
      enabled: true,
      tsconfig: './tsconfig.json',
    },
    server: {
      deps: {
        inline: ['@mui/x-data-grid'],
      },
    },
    coverage: {
      enabled: true,
      include: ['src/**/*.{ts,tsx}'], // Adjust the glob pattern if needed
      exclude: [
        'src/**/*.stories.{ts,tsx}',
        'src/**/*.test.{ts,tsx}',
        'src/**/*.spec.{ts,tsx}',
        'src/**/constants/**',
        'src/**/types/**',
        'src/**/router/**',
        'src/**/styles/**',
        'src/**/mocks/**',
        'src/**/*.constant.ts',
        'src/**/*.constants.ts',
        'src/**/*.d.ts',
        'src/**/*.type.ts',
        'src/**/*.types.ts',
        'src/**/*.style.{ts,tsx}',
        'src/**/*.styles.{ts,tsx}',
        'src/**/*.mock.{ts,tsx}',
        'src/**/index.ts',
        'src/app/layout.tsx',
        'src/theme/**',
      ],
      provider: 'v8',
      reporter: ['text', 'html', 'lcov'],
      reportsDirectory: './coverage',
      // Enable the following line to enforce stricter code coverage thresholds when committing code
      // thresholds: {
      //   statements: 80,
      //   branches: 80,
      //   functions: 80,
      //   lines: 80,
      // },
    },
    testTimeout: 60000,
  },
});
